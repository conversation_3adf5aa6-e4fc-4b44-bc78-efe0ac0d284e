<script setup lang="ts">
// 会议现场照片组件
import { message } from 'ant-design-vue';
import { onMounted, ref, watch, nextTick, defineProps, defineEmits } from 'vue';
import { fileApi } from '@haierbusiness-front/apis';
import { UploadFile } from '@haierbusiness-front/common-libs';
import { UploadOutlined } from '@ant-design/icons-vue';

// 类型定义
interface ConferencePhotoItem {
  tempId: string;                // 临时ID
  serialNumber: number;          // 序号（仅用于显示）
  subType: string;               // 类型（用户输入）
  paths: string[];               // 附件路径数组
  photos: UploadFile[];          // 附件文件列表
}

// 文件上传相关常量
const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
const FILE_SIZE_LIMIT = 10; // MB
const UPLOAD_ACCEPT = '.jpg,.jpeg,.png,.gif';



const props = defineProps({
  conferencePhotoList: {
    type: Array as () => ConferencePhotoItem[],
    default: () => [],
  },
});

const emit = defineEmits(['conferencePhotosEmit']);

// 响应式数据
const uploadLoading = ref(false);
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 初始化标志，防止重复初始化
const isInitialized = ref(false);

// 从缓存数据初始化会议现场照片
const initConferencePhotosFromCache = (cacheData: ConferencePhotoItem[]) => {
  if (!cacheData || cacheData.length === 0) {
    return;
  }

  console.log('🔄 会议现场照片 - 开始从缓存恢复数据:', cacheData);

  // 处理缓存数据，将 paths 转换为 photos
  const processedData = cacheData.map((item: ConferencePhotoItem, index: number) => {
    const processedItem = { ...item };

    // 处理附件数据 - 将字符串路径转换为 UploadFile 对象
    if (item.paths && Array.isArray(item.paths) && item.paths.length > 0) {
      processedItem.photos = item.paths.map((path: string, fileIndex: number) => {
        // 处理路径，确保正确的 URL 格式
        let processedPath = path;

        // 如果路径已经包含完整 URL，提取相对路径
        if (path.includes(baseUrl)) {
          processedPath = path.replace(baseUrl, '');
        }

        // 确保路径以 / 开头
        if (!processedPath.startsWith('/')) {
          processedPath = '/' + processedPath;
        }

        return {
          uid: `${item.tempId || Date.now()}_cache_${fileIndex}`,
          name: path.split('/').pop() || `照片${fileIndex + 1}`,
          status: 'done' as const,
          url: baseUrl + processedPath,
          filePath: processedPath,
          fileName: path.split('/').pop() || `照片${fileIndex + 1}`,
        };
      });
    } else {
      processedItem.photos = [];
    }

    // 确保序号正确
    processedItem.serialNumber = index + 1;

    console.log(`✅ 会议现场照片 ${index + 1} 恢复完成:`, processedItem);
    return processedItem;
  });

  isInitialized.value = true;
  console.log('🎉 会议现场照片 - 缓存数据恢复完成，共恢复', processedData.length, '条记录');

  // 发射处理后的数据到父组件
  emit('conferencePhotosEmit', processedData);
};

// 获取文件显示名称
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '';
  
  const maxLength = 15;
  if (fileName.length <= maxLength) return fileName;
  
  const extension = fileName.split('.').pop() || '';
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';
  
  return `${truncatedName}.${extension}`;
};

// 文件上传前验证
const beforeUpload = (file: File): boolean => {
  const isValidType = SUPPORTED_IMAGE_TYPES.includes(file.type);
  
  if (!isValidType) {
    message.error('只支持上传 JPG、PNG、GIF 格式的图片！');
    return false;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`图片大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return false;
  }

  return true; // 验证通过，允许上传
};

// 新增附件分类
const handleAddPhotoCategory = () => {
  const newItem: ConferencePhotoItem = {
    tempId: `${Date.now()}_${Math.random()}`,
    serialNumber: props.conferencePhotoList.length + 1,
    subType: '',
    paths: [],
    photos: [],
  };

  const updatedList = [...props.conferencePhotoList, newItem];
  emit('conferencePhotosEmit', updatedList);
};

// 更新照片字段
const updatePhoto = (itemId: string, field: string, value: any) => {
  const updatedList = props.conferencePhotoList.map((item) => {
    if (item.tempId === itemId) {
      return { ...item, [field]: value };
    }
    return item;
  });
  emit('conferencePhotosEmit', updatedList);
};

// 照片上传处理
const handlePhotoUpload = (options: any, itemId: string) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((response) => {
      const fileObj: UploadFile = {
        uid: options.file.uid || Date.now().toString(),
        name: options.file.name,
        status: 'done',
        url: response.path ? baseUrl + response.path : '',
        filePath: response.path ? baseUrl + response.path : '',
        fileName: options.file.name,
      };

      const updatedList = props.conferencePhotoList.map((item) => {
        if (item.tempId === itemId) {
          return {
            ...item,
            photos: [...(item.photos || []), fileObj],
            paths: [...(item.paths || []), response.path ? baseUrl + response.path : ''],
          };
        }
        return item;
      });

      emit('conferencePhotosEmit', updatedList);
      message.success(`照片 ${options.file.name} 上传成功`);
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error(`照片 ${options.file.name} 上传失败，请重试`);
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 删除照片
const handleRemovePhoto = (file: UploadFile, itemId: string) => {
  const updatedList = props.conferencePhotoList.map((item) => {
    if (item.tempId === itemId) {
      const photoIndex = item.photos.findIndex(photo => photo.uid === file.uid);
      if (photoIndex > -1) {
        const newPhotos = [...item.photos];
        const newPaths = [...item.paths];
        newPhotos.splice(photoIndex, 1);
        newPaths.splice(photoIndex, 1);
        
        return {
          ...item,
          photos: newPhotos,
          paths: newPaths,
        };
      }
    }
    return item;
  });
  emit('conferencePhotosEmit', updatedList);
  message.success('照片删除成功');
};

// 查看分类详情
const viewConferencePhotoCategory = (itemId: string) => {
  const item = props.conferencePhotoList.find(item => item.tempId === itemId);
  if (item) {
    console.log('查看会议现场照片分类:', item);
    // 这里可以添加查看逻辑，比如打开模态框显示详情
  }
};


// 文件预览
const handlePreviewPhoto = (file: UploadFile) => {
  previewImage.value = file.url || file.filePath || '';
  previewVisible.value = true;
  previewTitle.value = file.name || '文件预览';
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewImage.value = '';
  previewTitle.value = '';
};

// 监听 conferencePhotoList 变化，处理缓存数据回显
watch(() => props.conferencePhotoList, (newList) => {
  if (newList && newList.length > 0 && !isInitialized.value) {
    console.log('📥 会议现场照片 - 检测到缓存数据:', newList);

    // 检查是否需要处理附件回显或序号修复
    const needsProcessing = newList.some(item => {
      const hasPathsButNoPhotos = item.paths && item.paths.length > 0 &&
        (!item.photos || !Array.isArray(item.photos) || item.photos.length === 0);
      const hasIncorrectSerialNumber = !item.serialNumber || item.serialNumber === 0;

      console.log(`🔍 会议现场照片 - 检查项目 ${item.subType || '未命名'}:`, {
        paths: item.paths?.length || 0,
        photos: item.photos?.length || 0,
        serialNumber: item.serialNumber,
        hasPathsButNoPhotos,
        hasIncorrectSerialNumber
      });

      return hasPathsButNoPhotos || hasIncorrectSerialNumber;
    });

    if (needsProcessing) {
      initConferencePhotosFromCache(newList);
    } else {
      // 即使不需要处理附件，也要确保序号正确
      const fixedList = newList.map((item, index) => ({
        ...item,
        serialNumber: index + 1
      }));
      emit('conferencePhotosEmit', fixedList);
      isInitialized.value = true;
    }
  }
}, { immediate: true, deep: true });

// 额外监听，确保序号始终正确（用于解决刷新后序号丢失的问题）
watch(() => props.conferencePhotoList, (newList) => {
  if (newList && newList.length > 0) {
    // 检查是否有序号缺失或错误的情况
    const hasIncorrectSerialNumbers = newList.some((item, index) =>
      !item.serialNumber || item.serialNumber !== (index + 1)
    );

    if (hasIncorrectSerialNumbers && isInitialized.value) {
      console.log('🔧 会议现场照片 - 修复序号问题');
      const fixedList = newList.map((item, index) => ({
        ...item,
        serialNumber: index + 1
      }));
      emit('conferencePhotosEmit', fixedList);
    }
  }
}, { deep: true });

onMounted(() => {
  // 等待一下，让 watch 先执行
  nextTick(() => {
    // 只有在没有缓存数据时才初始化空数据
    if ((!props.conferencePhotoList || props.conferencePhotoList.length === 0) && !isInitialized.value) {
      console.log('🆕 会议现场照片 - 没有缓存数据，添加默认空行');
      handleAddPhotoCategory();
      isInitialized.value = true;
    }
  });
});
</script>

<template>
  <!-- 会议现场照片 -->
  <div class="scheme_conference_photos">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>会议现场照片</span>
    </div>
    
    <!-- 表格布局 -->
    <div class="info-table-wrapper conference-photos-table">
      <div class="table-header">
        <div class="col-serial">序号</div>
        <div class="col-type">类型</div>
        <div class="col-files">附件</div>
        <div class="col-action">操作</div>
      </div>
      
      <div class="table-body">
        <div v-for="(item, index) in conferencePhotoList" :key="item.tempId" class="table-row">
          <!-- 序号 -->
          <div class="col-serial">
            {{ item.serialNumber || (index + 1) }}
          </div>

          <!-- 类型 -->
          <div class="col-type">
            <a-input
              :value="item.subType"
              placeholder="请输入类型"
              size="small"
              class="borderless-input"
              :bordered="false"
              @change="(e: Event) => updatePhoto(item.tempId, 'subType', (e.target as HTMLInputElement).value)"
            />
          </div>

          <!-- 附件 -->
          <div class="col-files">
            <div class="files-content">
              <!-- 已上传文件标签 -->
              <div class="file-tags" v-if="item.photos && item.photos.length > 0">
                <a-tag
                  v-for="photo in item.photos"
                  :key="photo.uid"
                  closable
                  class="file-tag"
                  @click="() => handlePreviewPhoto(photo)"
                  @close="() => handleRemovePhoto(photo, item.tempId)"
                >
                  {{ getFileDisplayName(photo.name) }}
                </a-tag>
              </div>

              <!-- 上传按钮 -->
              <a-upload
                :file-list="[]"
                :before-upload="beforeUpload"
                :custom-request="(options: any) => handlePhotoUpload(options, item.tempId)"
                :show-upload-list="false"
                :accept="UPLOAD_ACCEPT"
                multiple
              >
                <a-button size="small" type="link" :loading="uploadLoading">
                  <upload-outlined />
                  上传附件
                </a-button>
              </a-upload>
            </div>
          </div>

          <!-- 操作 -->
          <div class="col-action">
            <a-button
              type="link"
              size="small"
              @click="() => viewConferencePhotoCategory(item.tempId)"
            >
              查看
            </a-button>
          </div>
        </div>

        <!-- 添加按钮行 -->
        <div class="table-row add-row">
          <div class="add-button-full-width" @click="handleAddPhotoCategory">
            <div class="demand_add">
              <div class="demand_add_img mr8"></div>
              <span>添加图片</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal v-model:open="previewVisible" title="文件预览" :footer="null" width="80%" @cancel="handlePreviewCancel">
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewTitle }}</h4>
        </div>
        <div class="preview-body">
          <img
            v-if="previewImage"
            :src="previewImage"
            alt="照片预览"
            style="width: 100%; max-height: 600px; object-fit: contain"
          />
          <div v-else class="no-image">
            <p>暂无可预览的照片内容</p>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
*{
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}
.scheme_conference_photos {
  position: relative;
  margin-bottom: 24px;

  .interact_title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;

    .interact_shu {
      width: 4px;
      height: 20px;
      background: #1868db;
      border-radius: 2px;
    }

    span {
      font-size: 18px;
      font-weight: 500;
      color: #1d2129;
    }

    .tip-text {
      margin-left: 16px;
      font-size: 12px;
      color: #ff4d4f;
      font-weight: normal;
    }
  }

  .info-table-wrapper {
    width: 100%;
    border: none;
    border-radius: 0;
    margin-bottom: 0;

    .table-header {
      display: flex;
      background-color: #fafafa;
      font-weight: 500;
      font-size: 14px;
      color: #333;

      > div {
        padding: 12px 8px;
        text-align: center;
      }

      .col-serial {
        width: 80px;
        // border-right: 1px solid #d9d9d9;
      }
      .col-type {
        width: 200px;
        // border-right: 1px solid #d9d9d9;
      }
      .col-files {
        flex: 1;
        min-width: 200px;
        // border-right: 1px solid #d9d9d9;
      }
      .col-action {
        width: 120px;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &.add-row {
          border-bottom: none;

          .add-button-full-width {
            width: 100%;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            min-height: 38px;
            cursor: pointer;
            border-bottom: none;

            &:hover {
              background-color: #f5f5f5;
            }

            .demand_add {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              color: #1890ff;
              font-size: 14px;
              margin-left: 8px;

              .demand_add_img {
                width: 16px;
                height: 16px;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+') no-repeat center;
                background-size: contain;
                margin-right: 8px;
              }
            }
          }
        }

        > div {
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 40px;
          // border-right: 1px solid #f0f0f0;

          &:last-child {
            border-right: none;
          }
        }

        .col-serial {
          width: 80px;
        }
        .col-type {
          width: 200px;
        }
        .col-files {
          flex: 1;
          min-width: 180px;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          gap: 4px;

          .files-content {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 4px;
            flex-wrap: wrap;
            justify-content: flex-start;

            .file-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
              justify-content: flex-start;
              max-width: 100%;

              .file-tag {
                cursor: pointer;
                font-size: 12px;
                background: transparent;
                border: 1px solid #1890ff;
                color: #1890ff;
                padding: 2px 8px;
                margin: 2px;
                border-radius: 2px;
                text-decoration: none;
                display: flex;
                align-items: center;
                gap: 2px;

            

                &:hover {
                  background-color: #f0f8ff;
                  color: #0e4ba1;
                  border-color: #0e4ba1;
                }
              }
            }
          }
        }
        .col-action {
          width: 120px;
          display: flex;
          justify-content: center;
        }
      }
    }
  }

  // 无边框输入框样式
  .borderless-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input {
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;
    }

    .ant-picker-input > input {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .no-image {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }
}

.mr8 {
  margin-right: 8px;
}

.mr20 {
  margin-right: 20px;
}

// 全局样式覆盖
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}
</style>
